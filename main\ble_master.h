/*
 * SPDX-FileCopyrightText: 2015-2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */
#ifndef H_BLECENT_
#define H_BLECENT_

#include <string.h>
#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_timer.h"
/* BLE */
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "host/util/util.h"
#include "services/gap/ble_svc_gap.h"
#include "esp_central.h"


#ifdef __cplusplus
extern "C" {
#endif


/**
 * @brief 从机响应数据结构体
 * 
 * 与BLE从机定义保持一致的数据结构
 */
struct gatts_slave_response {
    uint16_t device_id;        ///< 设备ID
    uint8_t device_mac[6];     ///< 设备MAC地址
    uint16_t battery_level;    ///< 电池电量百分比
    float temperature;         ///< 温度值(摄氏度)
    float pressure;           ///< 压力值(kPa)
};


#ifdef __cplusplus
}
#endif

#endif
