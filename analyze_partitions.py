#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32 分区表分析工具
分析8MB Flash的分区分配情况
"""

def analyze_partitions():
    # 新的分区表配置
    partitions = [
        ('bootloader', 0x0, 0x8000, '二级引导程序'),
        ('partition_table', 0x8000, 0x1000, '分区表'),
        ('nvs', 0x9000, 0x6000, 'NVS存储'),
        ('phy_init', 0xf000, 0x1000, 'PHY初始化数据'),
        ('ota_0', 0x10000, 0x180000, 'OTA应用分区0'),
        ('ota_1', 0x190000, 0x180000, 'OTA应用分区1'),
        ('otadata', 0x310000, 0x2000, 'OTA数据'),
        ('storage', 0x312000, 0x400000, 'SPIFFS存储'),
        ('reserved', 0x712000, 0xEE000, '预留空间')
    ]
    
    print("=" * 80)
    print("ESP32 8MB Flash 分区表分析")
    print("=" * 80)
    print(f"{'分区名称':<15} {'起始地址':<10} {'大小':<12} {'结束地址':<10} {'大小(KB)':<8} {'大小(MB)':<8} {'说明'}")
    print("-" * 80)
    
    total_used = 0
    for name, offset, size, desc in partitions:
        end_addr = offset + size
        size_kb = size // 1024
        size_mb = size / (1024 * 1024)
        total_used = max(total_used, end_addr)
        
        print(f"{name:<15} 0x{offset:06X}   0x{size:06X}   0x{end_addr:06X}   {size_kb:<8} {size_mb:<8.2f} {desc}")
    
    print("-" * 80)
    print(f"总使用空间: 0x{total_used:X} ({total_used//1024//1024:.1f}MB)")
    print(f"8MB Flash总空间: 0x800000 (8.0MB)")
    print(f"剩余空间: 0x{0x800000-total_used:X} ({(0x800000-total_used)//1024//1024:.1f}MB)")
    
    print("\n" + "=" * 80)
    print("固件大小分析")
    print("=" * 80)
    
    # 当前固件大小
    firmware_size = 652464  # 637KB
    ota_partition_size = 0x180000  # 1.5MB
    
    print(f"当前固件大小: {firmware_size} bytes ({firmware_size//1024}KB)")
    print(f"OTA分区大小: {ota_partition_size//1024}KB (1.5MB)")
    print(f"固件占用OTA分区比例: {firmware_size/ota_partition_size*100:.1f}%")
    print(f"OTA分区剩余空间: {(ota_partition_size-firmware_size)//1024}KB")
    
    print("\n" + "=" * 80)
    print("分区优化说明")
    print("=" * 80)
    print("1. NVS分区: 24KB - 足够存储WiFi配置、系统参数等")
    print("2. OTA分区: 每个1.5MB - 为固件增长预留充足空间")
    print("3. SPIFFS存储: 4MB - 大幅增加文件存储空间")
    print("4. 预留空间: 952KB - 可用于未来功能扩展")
    print("\n优势:")
    print("- 支持完整的OTA功能")
    print("- 大容量文件存储空间")
    print("- 为固件增长预留充足空间")
    print("- 保留灵活的扩展空间")

if __name__ == "__main__":
    analyze_partitions()
