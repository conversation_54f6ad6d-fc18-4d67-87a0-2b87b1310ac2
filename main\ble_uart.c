/*
 * SPDX-FileCopyrightText: 2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#include "ble_uart.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include "esp_log.h"
#include <stdio.h>
#include <string.h>

/* UART 配置参数 */
#define UART_PORT_NUM      UART_NUM_1
#define UART_BAUD_RATE     115200
#define UART_DATA_BITS     UART_DATA_8_BITS
#define UART_PARITY        UART_PARITY_DISABLE
#define UART_STOP_BITS     UART_STOP_BITS_1
#define UART_FLOW_CTRL     UART_HW_FLOWCTRL_DISABLE
#define UART_SOURCE_CLK    UART_SCLK_DEFAULT
//
#define UART_TX_PIN        GPIO_NUM_15
#define UART_RX_PIN        GPIO_NUM_23
#define UART_RTS_PIN       UART_PIN_NO_CHANGE
#define UART_CTS_PIN       UART_PIN_NO_CHANGE
//
#define UART_BUF_SIZE      1024
//
static const char *TAG = "BLE_UART";
static bool uart_initialized = false;

esp_err_t ble_uart_init(void)
{
    if (uart_initialized) {
        ESP_LOGW(TAG, "UART already initialized");
        return ESP_OK;
    }

    const uart_config_t uart_config = {
        .baud_rate = UART_BAUD_RATE,
        .data_bits = UART_DATA_BITS,
        .parity = UART_PARITY,
        .stop_bits = UART_STOP_BITS,
        .flow_ctrl = UART_FLOW_CTRL,
        .source_clk = UART_SOURCE_CLK,
    };

    esp_err_t ret;

    // 安装UART驱动
    ret = uart_driver_install(UART_PORT_NUM, UART_BUF_SIZE * 2, 0, 0, NULL, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to install UART driver: %s", esp_err_to_name(ret));
        return ret;
    }

    // 配置UART参数
    ret = uart_param_config(UART_PORT_NUM, &uart_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure UART parameters: %s", esp_err_to_name(ret));
        uart_driver_delete(UART_PORT_NUM);
        return ret;
    }

    // 设置UART引脚
    ret = uart_set_pin(UART_PORT_NUM, UART_TX_PIN, UART_RX_PIN, UART_RTS_PIN, UART_CTS_PIN);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set UART pins: %s", esp_err_to_name(ret));
        uart_driver_delete(UART_PORT_NUM);
        return ret;
    }

    uart_initialized = true;
    ESP_LOGI(TAG, "UART initialized successfully (TX: GPIO%d, RX: GPIO%d, Baud: %d)", 
             UART_TX_PIN, UART_RX_PIN, UART_BAUD_RATE);

    return ESP_OK;
}

esp_err_t ble_uart_send_raw_data(const uint8_t *data, size_t length)
{
    if (!uart_initialized) {
        ESP_LOGE(TAG, "UART not initialized");
        return ESP_FAIL;
    }

    if (data == NULL) {
        ESP_LOGE(TAG, "Invalid data pointer");
        return ESP_ERR_INVALID_ARG;
    }

    if (length == 0) {
        ESP_LOGW(TAG, "Data length is zero");
        return ESP_OK;
    }

    // 通过UART发送原始二进制数据
    int bytes_written = uart_write_bytes(UART_PORT_NUM, data, length);
    if (bytes_written != length) {
        ESP_LOGW(TAG, "UART write incomplete: written %d of %zu bytes", bytes_written, length);
        return ESP_FAIL;
    }

    ESP_LOGD(TAG, "UART raw data sent: %zu bytes", length);
    ESP_LOG_BUFFER_HEXDUMP(TAG, data, length, ESP_LOG_DEBUG);

    return ESP_OK;
}

esp_err_t ble_uart_deinit(void)
{
    if (!uart_initialized) {
        ESP_LOGW(TAG, "UART not initialized");
        return ESP_OK;
    }

    esp_err_t ret = uart_driver_delete(UART_PORT_NUM);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to delete UART driver: %s", esp_err_to_name(ret));
        return ret;
    }

    uart_initialized = false;
    ESP_LOGI(TAG, "UART deinitialized successfully");

    return ESP_OK;
}
