/*
 * ESP32 BLE Mill Monitor Master - Main Application Entry Point
 * 
 * This file contains the main application initialization and coordination
 * of all system components including BLE, WiFi, file system, and HTTP server.
 */

#include <stdio.h>
#include <string.h>
#include "esp_log.h"
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// Component headers
#include "ble_master.h"
#include "wifi_ap.h"
#include "fs_mount.h"
#include "file_server.h"

static const char *TAG = "MAIN";

// File system mount point
#define MOUNT_POINT "/spiffs"

/**
 * @brief Initialize all system components
 * 
 * @return esp_err_t ESP_OK on success, error code on failure
 */
static esp_err_t system_init(void)
{
    esp_err_t ret;
    
    ESP_LOGI(TAG, "Starting ESP32 BLE Mill Monitor Master");
    ESP_LOGI(TAG, "========================================");
    
    // 1. Initialize BLE Master functionality
    ESP_LOGI(TAG, "1. Initializing BLE Master...");
    ret = ble_master_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize BLE Master: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "✓ BLE Master initialized successfully");
    
    // 2. Initialize WiFi AP
    ESP_LOGI(TAG, "2. Initializing WiFi AP...");
    ret = wifi_init_ap();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize WiFi AP: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "✓ WiFi AP initialized successfully");
    
    // 3. Initialize file system
    ESP_LOGI(TAG, "3. Initializing SPIFFS file system...");
    ret = example_mount_storage(MOUNT_POINT);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize SPIFFS: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "✓ SPIFFS file system initialized successfully");
    
    // 4. Start HTTP file server
    ESP_LOGI(TAG, "4. Starting HTTP file server...");
    ret = example_start_file_server(MOUNT_POINT);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start HTTP file server: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "✓ HTTP file server started successfully");
    
    ESP_LOGI(TAG, "========================================");
    ESP_LOGI(TAG, "All systems initialized successfully!");
    ESP_LOGI(TAG, "- BLE Master: Scanning and connecting to slaves");
    ESP_LOGI(TAG, "- WiFi AP: ESP32-FileServer (password: 12345678)");
    ESP_LOGI(TAG, "- HTTP Server: http://192.168.4.1/");
    ESP_LOGI(TAG, "- File System: SPIFFS mounted at %s", MOUNT_POINT);
    ESP_LOGI(TAG, "========================================");
    
    return ESP_OK;
}

/**
 * @brief Main application entry point
 */
void app_main(void)
{
    esp_err_t ret = system_init();
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "System initialization failed with error: %s", esp_err_to_name(ret));
        ESP_LOGE(TAG, "System will restart in 5 seconds...");
        vTaskDelay(pdMS_TO_TICKS(5000));
        esp_restart();
        return;
    }
    
    // Main application loop - monitor system status
    ESP_LOGI(TAG, "Entering main application loop...");
    
    while (1) {
        // System is now running, all components are initialized
        // BLE runs in its own task
        // WiFi and HTTP server handle requests automatically
        // Main task can be used for system monitoring or other tasks
        
        vTaskDelay(pdMS_TO_TICKS(10000)); // Sleep for 10 seconds
        
        // Optional: Add system health monitoring here
        // ESP_LOGI(TAG, "System running normally...");
    }
}
