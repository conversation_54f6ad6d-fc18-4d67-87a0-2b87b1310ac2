/*
 * ESP32 BLE Mill Monitor Master - Main Application Entry Point
 * 
 * This file contains the main application initialization and coordination
 * of all system components including BLE, WiFi, file system, and HTTP server.
 */

#include <stdio.h>
#include <string.h>
#include "esp_log.h"
#include "esp_err.h"
#include "esp_system.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// Component headers
#include "ble_master.h"
#include "wifi_ap.h"
#include "fs_mount.h"
#include "file_server.h"

static const char *TAG = "MAIN";

// File system mount point
#define MOUNT_POINT "/spiffs"



// BLE和WiFi初始化任务函数
void app_init_task(void *pvParameters)
{

    // 启用WIFI AP做本地OTA
    wifi_init();

    // 任务完成后删除自己
    vTaskDelete(NULL);
}

void app_main(void)
{
    esp_err_t ret;

    // NVS（Non-Volatile Storage） 是一种非易失性存储系统，主要用于在设备掉电或重启后保留数据
    // 通常是以 key-value（键值对） 的形式存储数据, 保存配置参数（如 WiFi 名称、密码）
    ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
   
    // 初始化uart
    

    // 初始化WiFi AP
    ret = wifi_init_ap();
    if (ret == ESP_OK)
    {
        ESP_LOGI(TAG, "WiFi AP initialization completed");

        // 挂载文件系统
        const char *base_path = "/data";
        ret = example_mount_storage(base_path);
        ESP_ERROR_CHECK(ret);

        // 启动文件服务器
        ret = example_start_file_server(base_path);
        ESP_ERROR_CHECK(ret);
    }
    else
    {
        ESP_LOGE(TAG, "Failed to initialize WiFi AP");
    }


    ESP_LOGW(TAG, "master-222");
    
}
